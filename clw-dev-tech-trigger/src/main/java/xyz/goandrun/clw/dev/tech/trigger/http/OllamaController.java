package xyz.goandrun.clw.dev.tech.trigger.http;

import jakarta.annotation.Resource;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.vectorstore.PgVectorStore;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.web.bind.annotation.*;
import org.springframework.ai.ollama.OllamaChatClient;
import reactor.core.publisher.Flux;
import xyz.goandrun.clw.dev.tech.api.IAiService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/11 9:34
 */

@RestController()
@CrossOrigin("*")
@RequestMapping("/api/v1/ollama/")
public class OllamaController implements IAiService {

    @Resource
    private OllamaChatClient chatClient;
    @Resource
    private PgVectorStore pgVectorStore;

    /**
     * generate (一次性问答模式)：用于后台和 API
     * http://localhost:8090/api/v1/ollama/generate?model=deepseek-r1:1.5b&message=1+1
     */
    @RequestMapping(value = "generate", method = RequestMethod.GET)
    @Override
    public ChatResponse generate(@RequestParam String model, @RequestParam String message) {
        //call 方法 (一次性返回)
        //作用: 执行一次阻塞式的调用。你的程序会一直等待，直到 AI 完全生成好所有回复，然后把一个完整的 ChatResponse 对象一次性返回给你
        //适用场景: 适合那些回复较短、可以快速生成的场景，或者后台的批处理任务。
        return chatClient.call(new Prompt(message, OllamaOptions.create().withModel(model)));
    }

    /**
     * generateStream (流式对话模式)：用于前端和交互。
     * http://localhost:8090/api/v1/ollama/generate_stream?model=deepseek-r1:1.5b&message=hi
     */
    @RequestMapping(value = "generate_stream", method = RequestMethod.GET)
    @Override
    public Flux<ChatResponse> generateStream(@RequestParam String model, @RequestParam String message) {
        //作用: 执行一次非阻塞的、流式的调用。它会立刻返回一个 Flux<ChatResponse> 对象。
        //Flux 是什么: Flux 是来自于 Project Reactor（Spring 5 之后深度集成的响应式编程框架）的一个核心类。你可以把它理解为一个“未来的数据流”或“事件发布者”。当 AI 开始生成回复时，它会一个词一个词或一小段一小段地把结果通过这个 Flux 推送给客户端。
        //心-智模型: 这就像你看 ChatGPT 打字一样。它不是等全部想好了再显示，而是一个字一个字地“流”到你的屏幕上。
        //适用场景: 所有需要即时反馈的用户交互界面。这极大地提升了用户体验，因为用户不需要长时间地等待第一个字符的出现。
        return chatClient.stream(new Prompt(message, OllamaOptions.create().withModel(model)));
    }

    @RequestMapping(value = "generate_stream_rag", method = RequestMethod.GET)
    @Override
    public Flux<ChatResponse> generateStreamRag(@RequestParam String model, @RequestParam String ragTag, @RequestParam String message) {
        String SYSTEM_PROMPT = """
                Use the information from the DOCUMENTS section to provide accurate answers but act as if you knew this information innately.
                If unsure, simply state that you don't know.
                Another thing you need to note is that your reply must be in Chinese!
                DOCUMENTS:
                    {documents}
                """;

        // 指定文档搜索
        SearchRequest request = SearchRequest.query(message)
                .withTopK(5)
                .withFilterExpression("knowledge == '" + ragTag + "'");

        List<Document> documents = pgVectorStore.similaritySearch(request);
        String documentCollectors = documents.stream().map(Document::getContent).collect(Collectors.joining());
        Message ragMessage = new SystemPromptTemplate(SYSTEM_PROMPT).createMessage(Map.of("documents", documentCollectors));

        List<Message> messages = new ArrayList<>();
        messages.add(new UserMessage(message));
        messages.add(ragMessage);

        return chatClient.stream(new Prompt(
                messages,
                OllamaOptions.create()
                        .withModel(model)
        ));
    }

}
