package xyz.goandrun.clw.dev.tech.config;


import org.springframework.ai.ollama.OllamaChatClient;
import org.springframework.ai.ollama.OllamaEmbeddingClient;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.PgVectorStore;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class OllamaConfig {
    //负责处理所有与 Ollama 服务器之间的底层 HTTP 通信
    @Bean
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }
    //一个专门用来和 Ollama 的聊天模型进行交互的客户端。
    // 它比 OllamaApi 更高级，封装了聊天的特定逻辑，比如发送带有角色（用户、系统、助手）的消息列表
    @Bean
    public OllamaChatClient ollamaChatClient(OllamaApi ollamaApi) {
        return new OllamaChatClient(ollamaApi);
    }

    @Bean
    public TokenTextSplitter tokenTextSplitter() {
        return new TokenTextSplitter();
    }
    //一个内存中的向量数据库。它非常简单，所有的文本块和对应的向量都存储在程序的内存（RAM）里
//    优点是配置简单，无需外部数据库，非常适合快速原型开发和测试
//    缺点是数据不持久，一旦程序关闭，所有存储的向量都会丢失
    @Bean
    public SimpleVectorStore simpleVectorStore(OllamaApi ollamaApi) {
        OllamaEmbeddingClient embeddingClient = new OllamaEmbeddingClient(ollamaApi);
        embeddingClient.withDefaultOptions(OllamaOptions.create().withModel("nomic-embed-text"));
        return new SimpleVectorStore(embeddingClient);
    }
    //一个持久化的向量数据库，它使用 PostgreSQL + pgvector 扩展来存储向量
//    PgVectorStore 将所有数据保存在数据库中，数据是持久的，程序重启后数据依然存在
    @Bean
    public PgVectorStore pgVectorStore(OllamaApi ollamaApi, JdbcTemplate jdbcTemplate) {
        OllamaEmbeddingClient embeddingClient = new OllamaEmbeddingClient(ollamaApi);
        embeddingClient.withDefaultOptions(OllamaOptions.create().withModel("nomic-embed-text"));
        return new PgVectorStore(jdbcTemplate, embeddingClient);
    }

}
