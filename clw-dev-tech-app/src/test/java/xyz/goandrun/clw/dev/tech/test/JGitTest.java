package xyz.goandrun.clw.dev.tech.test;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.OllamaChatClient;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.PgVectorStore;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.PathResource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.List;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/18 15:34
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class JGitTest {

    @Resource
    private OllamaChatClient ollamaChatClient;
    //    一种智能的切分器，会尽量根据 token（可以理解为单词或字的一部分）数量来切分，而不是简单地按字数切，
    @Resource
    private TokenTextSplitter tokenTextSplitter;
    @Resource
    private SimpleVectorStore simpleVectorStore;
    @Resource
    private PgVectorStore pgVectorStore;

    @Test
    public void test() throws Exception {
        // 这部分替换为你的
        String repoURL = "https://github.com/World-controller/hm-dianping.git";
        String username = "GoAndRun";
        String password = "****************************************";

        String localPath = "./cloned-repo";
        log.info("克隆路径：" + new File(localPath).getAbsolutePath());

        FileUtils.deleteDirectory(new File(localPath));

        Git git = Git.cloneRepository()
                .setURI(repoURL)
                .setDirectory(new File(localPath))
                .setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password))
                .call();

        git.close();
    }

    @Test
    public void test_file() throws IOException {
        Files.walkFileTree(Paths.get("./cloned-repo"), new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
//                1.跳过 .git 目录
                if (dir.getFileName().toString().equals(".git")) {
                    log.info("Skipping .git directory: {}", dir);
                    return FileVisitResult.SKIP_SUBTREE;
                }
                return super.preVisitDirectory(dir, attrs);
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {

                // 2.过滤空文件
                if (attrs.size() == 0) {
                    log.info("Skipping empty file: {}", file.toString());
                    return FileVisitResult.CONTINUE;
                }
                log.info("文件路径:{}", file.toString());
                PathResource resource = new PathResource(file);
                TikaDocumentReader reader = new TikaDocumentReader(resource);
                List<Document> documents;
//                3.增加异常处理：我引入了 try-catch 块，用于捕获在文件读取过程中可能出现的异常。
//                这样，即使某个文件读取失败，程序也会记录一条警告并继续处理下一个文件，而不是直接崩溃
                try {
                    documents = reader.get();
                } catch (Exception e) {
                    log.warn("Error reading file {}: {}", file, e.getMessage());
                    return FileVisitResult.CONTINUE;
                }

                if (documents.isEmpty()) {
                    return FileVisitResult.CONTINUE;
                }

                List<Document> documentSplitterList = tokenTextSplitter.apply(documents);

                documents.forEach(doc -> doc.getMetadata().put("knowledge", "S-PAY-MALL"));
                documentSplitterList.forEach(doc -> doc.getMetadata().put("knowledge", "S-PAY-MALL"));

                pgVectorStore.accept(documentSplitterList);

                return FileVisitResult.CONTINUE;
            }
        });
    }



}
