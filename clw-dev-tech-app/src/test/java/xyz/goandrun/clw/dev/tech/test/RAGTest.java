package xyz.goandrun.clw.dev.tech.test;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.OllamaChatClient;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.PgVectorStore;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/12 21:49
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RAGTest {

    @Resource
    private OllamaChatClient ollamaChatClient;
//    一种智能的切分器，会尽量根据 token（可以理解为单词或字的一部分）数量来切分，而不是简单地按字数切，
    @Resource
    private TokenTextSplitter tokenTextSplitter;
    @Resource
    private SimpleVectorStore simpleVectorStore;
    @Resource
    private PgVectorStore pgVectorStore;

    @Test
    public void upload() {

//       第一步：指定数据源。TikaDocumentReader能处理各种格式的文件
        TikaDocumentReader reader = new TikaDocumentReader("./data/file.text");

//      第二步：将非结构化的文件内容，转换成 Spring AI 框架能够理解和处理的标准化结构
//     原因： 一个 Document 对象不仅包含文本内容，还包含元数据（metadata），比如文件名、路径等。
        List<Document> documents = reader.get();
        System.out.println(documents);

//        第三步：将上一步得到的 `Document` 列表进行**切分**，得到一个可能包含更多 `Document` 对象的新列表
//        原因：1.  **上下文窗口限制**：语言模型一次能处理的文本长度是有限的（即“上下文窗口”）。如果整个文档非常长，一次性丢给模型，它处理不了。
//        2.  **检索精度**：我们通常不关心整个文档，只关心与问题最相关的那个**段落**。
//                    将文档切分成更小的、有意义的块（chunk），可以让我们在检索时更精确地定位到答案所在的位置。
        List<Document> documentSplitterList = tokenTextSplitter.apply(documents);
        System.out.println(documentSplitterList);

//        第四步：为每一个（切分前和切分后的）Document 对象添加一个自定义的元数据，键是 "knowledge"，值是 "知识库名称"。
//        原因：元数据非常有用，它允许我们在后续检索时进行过滤。
//        比如，你可以有好几个知识库（'公司规定'、'产品手册'），通过这个元数据，你可以指定只在 '产品手册' 这个知识库里进行搜索
        documents.forEach(doc -> doc.getMetadata().put("knowledge", "知识库名称"));
        System.out.println(documents);
        documentSplitterList.forEach(doc -> doc.getMetadata().put("knowledge", "知识库名称"));
        System.out.println(documentSplitterList);

//        第五步：调用 pgVectorStore 的 accept 方法，接收切分并处理好后的 Document 列表。
//        原因：这一步是知识入库的核心。accept 方法内部会自动完成两个关键操作：
//        1.调用嵌入模型：它会拿到配置的 OllamaEmbeddingClient（并且指定了模型为 nomic-embed-text），然后把每个 Document 的文本内容发送给这个模型，获取返回的向量。
//        2.存入数据库：它将每个 Document 的原始文本内容、元数据以及刚刚生成的向量，一并存入 PostgreSQL 数据库的指定表中。
        pgVectorStore.accept(documentSplitterList);

//        至此，upload 方法执行完毕，我们的外部文档就已经被转换成可供语义搜索的向量，并存储在 pgvector 数据库中了。
        log.info("上传完成");
    }

    @Test
    public void chat() {
//        第 1 步：定义问题和系统指令
        String message = "王大瓜，哪年出生";
        String SYSTEM_PROMPT = """
                Use the information from the DOCUMENTS section to provide accurate answers but act as if you knew this information innately.
                If unsure, simply state that you don't know.
                Another thing you need to note is that your reply must be in Chinese!
                DOCUMENTS:
                    {documents}
                """;
//     第 2 步：从向量数据库检索相关知识
        SearchRequest request = SearchRequest
                .query(message) //搜索的核心是用户的原始问题 message。
                .withTopK(5) // 我们希望找到最相关的 5 个文档块
                .withFilterExpression("knowledge == '知识库名称'"); //我们只在元数据中 knowledge 标签为 '知识库名称' 的文档中搜索。这呼应了 upload 方法里我们添加的元数据。
//        System.out.println(request);

        List<Document> documents = pgVectorStore.similaritySearch(request); //这是 RAG 的**“检索（Retrieval）”**核心步骤。
//        System.out.println(documents);
        String documentsCollectors = documents.stream().map(Document::getContent).collect(Collectors.joining()); //将检索到的多个 Document 对象的内容拼接成一个大的字符串。
//        System.out.println(documentsCollectors);

//        第 3 步：构建最终的提示词 (Prompt)
//        使用 SystemPromptTemplate，将上一步检索到的知识（documentsCollectors）填充到 SYSTEM_PROMPT 模板的 {documents} 占位符中，
//        生成一个最终的、完整的系统消息（ragMessage）
        Message ragMessage = new SystemPromptTemplate(SYSTEM_PROMPT).createMessage(Map.of("documents", documentsCollectors));
        System.out.println(ragMessage);
//        创建一个消息列表，包含两条消息：用户的原始问题 (UserMessage) 和我们刚刚构建的、包含了上下文知识的系统消息 (ragMessage)。
//        原因：我们不能只把用户的问题直接扔给 LLM，因为它的通用知识里可能没有“王大瓜”这个人。
//        我们必须把“问题”和“相关的上下文知识”一起提供给 LLM，这样它才能给出精准的回答
        ArrayList<Message> messages = new ArrayList<>();
        messages.add(new UserMessage(message));
        messages.add(ragMessage);
        System.out.println(messages);

//        第 4 步：调用 LLM 并获取回答（OllamaOptions 明确指定了负责生成最终答案的聊天模型是 deepseek-r1:1.5b）
//        关键点：注意这里我们用了和嵌入模型 (nomic-embed-text) 不同的聊天模型 (deepseek-r1:1.5b)。
//        这是因为它们的任务不同：前者负责将文本转换为向量（理解语义），后者负责根据上下文进行推理和生成流畅的自然语言回答
        ChatResponse chatResponse = ollamaChatClient.call(new Prompt(messages, OllamaOptions.create().withModel("deepseek-r1:1.5b")));
        System.out.println(chatResponse);
        log.info("测试结果:{}", JSON.toJSONString(chatResponse));

    }

}