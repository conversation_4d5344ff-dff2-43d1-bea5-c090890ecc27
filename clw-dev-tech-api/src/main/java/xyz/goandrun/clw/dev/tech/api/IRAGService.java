package xyz.goandrun.clw.dev.tech.api;

import org.springframework.web.multipart.MultipartFile;
import xyz.goandrun.clw.dev.tech.api.response.Response;

import java.util.List;

/**
 * <AUTHOR> https://github.com/World-controller @GoAndRun
 * @description
 * @create 2025/8/18 13:16
 */
public interface IRAGService {

    Response<List<String>> queryRagTagList();

    Response<String> uploadFile(String ragTag, List<MultipartFile> files);

    Response<String> analyzeGitRepository(String repoUrl, String userName, String token) throws Exception;

}
