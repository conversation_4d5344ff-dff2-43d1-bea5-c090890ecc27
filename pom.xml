<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>xyz.goandrun</groupId>
    <artifactId>ai-rag-knowledge-20602</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>clw-dev-tech-api</module>
        <module>clw-dev-tech-app</module>
        <module>clw-dev-tech-trigger</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-ai.version>0.8.1</spring-ai.version>
    </properties>

<!--   默认约定 (Convention):Maven 有一个内置的、最重要的约定：默认情况下，它会去一个叫做 Maven Central Repository 的中央仓库寻找你项目所需的所有依赖。 -->
<!--提供配置 (Configuration):
      但是，有些依赖包并不会发布到中央仓库，特别是：
      Milestone (里程碑) 版本： 比如 M5。这是一种预发布版本，功能已经基本稳定，但还不是最终的正式版。
      Snapshot (快照) 版本： 这是开发过程中的版本，可能每天都会更新，非常不稳定。-->
    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url> <!-- 指向了 Spring 的里程碑版本仓库的地址。-->
<!--            配置。它告诉 Maven：“不要在这个仓库里寻找 SNAPSHOT 版本的依赖。” 这保证了这个仓库的用途是纯粹的——只提供里程碑版本-->
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url><!-- 它指向了 Spring 的快照版本仓库。-->
<!--            这个配置告诉 Maven：“不要在这个仓库里寻找正式的 Release 版本或 Milestone 版本。” 它的用途也非常专一，只提供开发中的快照版本-->
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.3</version>
        <relativePath/>
    </parent>

    <dependencyManagement>
        <dependencies>
<!--          BOM (Bill of Materials) 文件本身就是一个“依赖版本规则手册”。  -->
<!--            Spring AI 有一套自己的“全家桶”，包含 spring-ai-core, spring-ai-openai, spring-ai-ollama 等很多模块。
               这个 BOM 文件规定了所有这些模块之间相互兼容的版本-->
<!--          通过导入它，你就等于接受了 Spring AI 的官方约定。以后你在子模块里用任何 Spring AI 的组件，都不用写版本号，而且能确保它们之间不会打架  -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.28</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.44.0</version>
            </dependency>
<!--    这是一个用来在 Java 代码中操作 Git 仓库的库。-->
            <dependency>
                <groupId>org.eclipse.jgit</groupId>
                <artifactId>org.eclipse.jgit</artifactId>
                <version>5.13.0.202109080827-r</version>
            </dependency>

            <dependency>
                <groupId>xyz.goandrun</groupId>
                <artifactId>clw-dev-tech-api</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>xyz.goandrun</groupId>
                <artifactId>clw-dev-tech-trigger</artifactId>
                <version>1.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <java_jvm>-Xms1G -Xmx1G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=test -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/clw-frame-archetype-lite-boot -Xloggc:/export/Logs/xfg-frame-archetype-lite-boot/gc-xfg-frame-archetype-lite-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>dev</profileActive>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <java_jvm>-Xms1G -Xmx1G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=test -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/clw-frame-archetype-lite-boot -Xloggc:/export/Logs/xfg-frame-archetype-lite-boot/gc-xfg-frame-archetype-lite-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>test</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <java_jvm>-Xms6G -Xmx6G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=release -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/fq-mall-activity-app -Xloggc:/export/Logs/xfg-frame-archetype-lite-boot/gc-xfg-frame-archetype-lite-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>prod</profileActive>
            </properties>
        </profile>
    </profiles>

</project>